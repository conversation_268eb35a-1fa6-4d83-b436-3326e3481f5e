from flask import Flask, jsonify, request
import mysql.connector
from mysql.connector import Error
from flask_cors import CORS
app = Flask(__name__)
CORS(app)

# 数据库连接配置
db_config = {
    'host': 'localhost',  # 数据库地址
    'user': 'root',       # 数据库用户名
    'password': '123456',  # 数据库密码
    'database': 'security'  # 数据库名称
}

# 测试路由
@app.route('/api/user', methods=['GET'])
def get_data():
    try:
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        if connection.is_connected():
            cursor = connection.cursor(dictionary=True)  # 使用字典形式返回数据
            query = "SELECT * FROM user"  # 替换为你的表名
            cursor.execute(query)
            result = cursor.fetchall()  # 获取所有查询结果
            return jsonify(result)  # 将结果以 JSON 格式返回
    except Error as e:
        return jsonify({'error': str(e)})
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == '__main__':
    app.run(debug=True)